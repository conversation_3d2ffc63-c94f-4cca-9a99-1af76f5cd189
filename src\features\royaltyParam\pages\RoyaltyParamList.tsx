import { keepPreviousData, useQueryClient } from '@tanstack/react-query';
import ContentHeader from 'components/partials/ContentHeader';
import ModalConfirm from 'components/partials/ModalConfirm';
import ModalContent from 'components/partials/ModalContent';
import PaginationTable from 'components/partials/PaginationTable';
import { PAGINATION, QUERY_KEY } from 'constants/common';
import { useGraphQLMutation } from 'hooks/useGraphQLMutation';
import { useGraphQLQuery } from 'hooks/useGraphQLQuery';
import useQueryParams from 'hooks/useQueryParams';
import { isEmpty, isUndefined, omitBy } from 'lodash';
import { useMemo, useState } from 'react';
import { Edit, Trash2 } from 'react-feather';
import { Helmet } from 'react-helmet-async';
import { useTranslation } from 'react-i18next';
import { Table } from 'reactstrap';
import {
    ROYALTY_PARAMS_CREATE,
    ROYALTY_PARAMS_DELETE,
    ROYALTY_PARAMS_LIST,
    ROYALTY_PARAMS_UPDATE,
} from 'services/RoyaltyParam';
import { useAppStore } from 'stores/appStore';
import { baseFilterConfig } from 'types/common';
import { ItemStatusNames } from 'types/common/Item';
import { SearchLayoutParam } from 'types/Layout';
import { RoyaltyParams, RoyaltyParamsForm, RoyaltyParamsQuery } from 'types/RoyaltyParams';
import { generateFilters, getFieldHtml, showToast } from 'utils/common';
import RoyaltyParamsContent from '../components/RoyaltyParamsContent';

export default function RoyaltyParamList() {
    const departmentId = useAppStore((state) => state.departmentId);
    const [isOpenUpdateModal, setIsOpenUpdateModal] = useState(false);
    const [isOpenWarningDelete, setIsOpenWarningDelete] = useState(false);
    const [selectedRow, setSelectedRow] = useState<RoyaltyParams | undefined>();

    const { t } = useTranslation();
    const queryClient = useQueryClient();

    const { queryParams, setQueryParams } = useQueryParams();
    const paramConfig: SearchLayoutParam = omitBy(
        {
            limit: queryParams.limit ?? PAGINATION.limit,
            page: queryParams.page ?? '1',
            department_id: departmentId.toString(),
        },
        isUndefined
    );

    const { limit, page, ...dataParamConfig } = paramConfig;
    const filters = generateFilters(dataParamConfig, baseFilterConfig);

    const { data } = useGraphQLQuery<RoyaltyParamsQuery>(
        [QUERY_KEY.ROYALTY_PARAMS, paramConfig, filters],
        ROYALTY_PARAMS_LIST,
        {
            page: Number(page),
            limit: Number(limit),
            filters: filters.length > 0 ? filters : undefined,
            sorts: ['display_order:ASC'],
        },
        '',
        {
            enabled: !!paramConfig.department_id,
            placeholderData: keepPreviousData,
        }
    );

    const createMutation = useGraphQLMutation<{}, { body: RoyaltyParams }>(ROYALTY_PARAMS_CREATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseUpdateModal(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });
    const updateMutation = useGraphQLMutation<{}, { id: number; body: RoyaltyParams }>(ROYALTY_PARAMS_UPDATE, '', {
        onSuccess: () => {
            showToast(true, [t('success.update')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseUpdateModal(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const deleteMutation = useGraphQLMutation(ROYALTY_PARAMS_DELETE, '', {
        onSuccess: () => {
            showToast(true, [t('success.delete')]);
            queryClient.invalidateQueries({
                queryKey: [QUERY_KEY.ROYALTY_PARAMS],
            });

            onCloseWarningDelete(false);
        },
        onError: () => showToast(false, [t('error.common')]),
    });

    const royaltyParams = useMemo(() => data?.royalty_params_list?.data || [], [data]);

    const handleEdit = (item: RoyaltyParams) => {
        setSelectedRow(item);
        onOpenUpdateModal();
    };

    const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
        setQueryParams({ ...queryParams, page: page.toString() });
    };

    const onOpenUpdateModal = () => {
        setIsOpenUpdateModal(true);
    };

    const onCloseUpdateModal = (isOpen: boolean) => {
        setIsOpenUpdateModal(isOpen);
        setSelectedRow(undefined);
    };

    const onSubmit = (data: RoyaltyParamsForm) => {
        const body = {
            ...data,
            display_order: Number(data.display_order),
            department_id: departmentId,
        };

        if (!isEmpty(selectedRow)) {
            updateMutation.mutate({
                id: selectedRow.id || 0,
                body,
            });
        } else {
            createMutation.mutate({
                body,
            });
        }
    };

    const onOpenWarningDelete = (item: RoyaltyParams) => {
        setIsOpenWarningDelete(true);
        setSelectedRow(item);
    };

    const onCloseWarningDelete = (isOpen: boolean) => {
        setIsOpenWarningDelete(isOpen);
        setSelectedRow(undefined);
    };

    const onDelete = () => {
        if (selectedRow?.id) {
            deleteMutation.mutate({
                id: selectedRow.id,
            });
        }
    };

    return (
        <>
            <Helmet>
                <title>Tham số nhuận bút</title>
            </Helmet>
            <ContentHeader
                title="Tham số nhuận bút"
                contextMenu={[
                    {
                        text: 'Thêm tham số',
                        icon: 'PLUS',
                        fnCallBack: {
                            actionMenu: () => onOpenUpdateModal(),
                        },
                    },
                ]}
            />

            <div className="content-body">
                <div className="card">
                    <Table responsive striped>
                        <thead>
                            <tr>
                                <th className="text-center w-[104px]">Thứ tự</th>
                                <th>Tên</th>
                                <th className="text-center">Trạng thái</th>
                                <th className="w-[64px]"></th>
                            </tr>
                        </thead>
                        <tbody>
                            {isEmpty(royaltyParams) ? (
                                <tr>
                                    <td colSpan={4} className="text-center">
                                        Không có tham số nhuận bút.
                                    </td>
                                </tr>
                            ) : (
                                <>
                                    {royaltyParams.map((item) => (
                                        <tr key={item.id}>
                                            <td className="text-center">{item.display_order}</td>
                                            <td>{item.name}</td>
                                            <td className="text-center">
                                                {getFieldHtml(ItemStatusNames, item.status_id as number, t)}
                                            </td>
                                            <td>
                                                <div className="d-flex gap-[4px]">
                                                    <button
                                                        type="button"
                                                        title="Xoá"
                                                        className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                                        onClick={() => handleEdit(item)}
                                                    >
                                                        <Edit size={14} />
                                                    </button>
                                                    <button
                                                        type="button"
                                                        title="Xoá"
                                                        className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                        onClick={() => onOpenWarningDelete(item)}
                                                    >
                                                        <Trash2 size={14} />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </>
                            )}
                        </tbody>
                    </Table>
                    <PaginationTable
                        countItem={data?.royalty_params_list.totalCount}
                        totalPage={data?.royalty_params_list.totalPages}
                        currentPage={data?.royalty_params_list.currentPage}
                        handlePageChange={handlePageChange}
                    />
                </div>
            </div>

            <ModalContent
                show={isOpenUpdateModal}
                changeShow={onCloseUpdateModal}
                title="Thêm tham số"
                content={
                    <RoyaltyParamsContent
                        selectedRoyaltyParam={selectedRow}
                        onSubmit={onSubmit}
                        isLoading={createMutation.isPending || updateMutation.isPending}
                        isOpen={isOpenUpdateModal}
                    />
                }
                modalSize="md"
            />

            <ModalConfirm
                show={isOpenWarningDelete}
                text={t('confirm.delete')}
                btnDisabled={deleteMutation.isPending}
                changeShow={onCloseWarningDelete}
                submitAction={onDelete}
            />
        </>
    );
}
