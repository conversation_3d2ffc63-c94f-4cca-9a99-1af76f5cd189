import { debounce } from 'lodash';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { Download, MoreHorizontal, Image, Video, Music, FileText, File } from 'react-feather';
import { FileData } from 'types/UploadFile';
import { DataList } from '../../../types/common';
import { FILE_TYPES, FileType } from '../../../constants/fileTypes';
import ModalConfirm from '../ModalConfirm';
import PaginationTable from '../PaginationTable';
import { FileCategory } from './ModalEditImage';

interface SearchFilters {
    startDate: string;
    endDate: string;
    caption: string;
    hashtag: string;
}

interface IProps {
    listFileCategories: Record<number, string>;
    listFile: DataList<FileData> | undefined;
    fileActive: number;
    onChooseFile: (file: FileData) => void;
    handlePageChange: (event: ChangeEvent<unknown>, page: number) => void;
    fileCategory: number;
    setFileCategory: (category: number) => void;
    searchFilters: SearchFilters;
    setSearchFilters: (filters: SearchFilters) => void;
    filesInArticle: FileData[];
    selectedFiles: { id: number; url: string; order: number; title: string }[];
    onFileSelect: (file: FileData) => void;
    getFileOrder: (fileId: number) => number;
    onDeleteFile: (fileId: number) => void;
    deletePending: boolean;
    fileType: FileType;
    allowMultiple?: boolean;
}

// Component để render preview cho từng loại file
const FilePreview = ({ file, fileType, isActive, onClick }: {
    file: FileData;
    fileType: FileType;
    isActive: boolean;
    onClick: () => void;
}) => {
    const baseClasses = 'w-full h-auto object-cover border-2 rounded-lg cursor-pointer';
    const activeClasses = isActive ? 'border-blue-500' : 'border-gray-200';
    
    switch (fileType) {
        case FILE_TYPES.IMAGE:
            return (
                <img
                    src={file.file_url || ''}
                    alt={file.file_name || ''}
                    className={`${baseClasses} ${activeClasses}`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                />
            );
        
        case FILE_TYPES.VIDEO:
            return (
                <div 
                    className={`${baseClasses} ${activeClasses} flex items-center justify-center bg-gray-100`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <Video size={48} className="text-gray-400" />
                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                        Video
                    </div>
                </div>
            );
        
        case FILE_TYPES.AUDIO:
            return (
                <div 
                    className={`${baseClasses} ${activeClasses} flex flex-col items-center justify-center bg-gray-100 p-4`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <Music size={48} className="text-gray-400 mb-2" />
                    <audio controls className="w-full" onClick={(e) => e.stopPropagation()}>
                        <source src={file.file_url} type={file.mime_type} />
                        Your browser does not support the audio element.
                    </audio>
                </div>
            );
        
        case FILE_TYPES.DOCUMENT:
            return (
                <div 
                    className={`${baseClasses} ${activeClasses} flex flex-col items-center justify-center bg-gray-100 p-4`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <FileText size={48} className="text-gray-400 mb-2" />
                    <span className="text-sm text-center text-gray-600 truncate w-full">
                        {file.file_name}
                    </span>
                </div>
            );
        
        case FILE_TYPES.ATTACHMENT:
        default:
            return (
                <div 
                    className={`${baseClasses} ${activeClasses} flex flex-col items-center justify-center bg-gray-100 p-4`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <File size={48} className="text-gray-400 mb-2" />
                    <span className="text-sm text-center text-gray-600 truncate w-full">
                        {file.file_name}
                    </span>
                </div>
            );
    }
};

export default function GenericFileSearchTab({
    listFileCategories,
    listFile,
    fileActive,
    onChooseFile,
    handlePageChange,
    fileCategory,
    setFileCategory,
    searchFilters,
    setSearchFilters,
    filesInArticle,
    selectedFiles,
    onFileSelect,
    getFileOrder,
    onDeleteFile,
    deletePending,
    fileType,
    allowMultiple = false,
}: Readonly<IProps>) {
    const [openDropdown, setOpenDropdown] = useState<number | null>(null);
    const [localFilters, setLocalFilters] = useState(searchFilters);
    const [fileGenerated, setFileGenerated] = useState<FileData[]>([]);
    const [fileDeleteId, setFileDeleteId] = useState<number | null>(null);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    // Debounced search function
    const debouncedSetSearchFilters = useMemo(
        () => debounce((filters: SearchFilters) => setSearchFilters(filters), 500),
        [setSearchFilters]
    );

    useEffect(() => {
        debouncedSetSearchFilters(localFilters);
        return () => {
            debouncedSetSearchFilters.cancel();
        };
    }, [localFilters, debouncedSetSearchFilters]);

    useEffect(() => {
        if (listFile?.data) {
            // Server đã filter files theo fileType rồi, không cần filter lại ở client
            setFileGenerated(listFile.data);
        }
    }, [listFile]);

    const handleFilterChange = (key: keyof SearchFilters, value: string) => {
        setLocalFilters(prev => ({ ...prev, [key]: value }));
    };

    const handleDeleteConfirm = () => {
        if (fileDeleteId) {
            onDeleteFile(fileDeleteId);
            setShowDeleteModal(false);
            setFileDeleteId(null);
        }
    };

    const handleFileClick = (file: FileData) => {
        if (allowMultiple) {
            onFileSelect(file);
        } else {
            onChooseFile(file);
        }
    };

    return (
        <>
            {/* Search Filters */}
            <div className="mb-4 space-y-3">
                {/* File Category Filter */}
                <div className="flex gap-2">
                    {Object.entries(listFileCategories).map(([key, value]) => (
                        <button
                            key={key}
                            type="button"
                            className={`px-3 py-1 text-sm rounded ${
                                fileCategory === +key
                                    ? 'bg-blue-500 text-white'
                                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                            }`}
                            onClick={() => setFileCategory(+key)}
                        >
                            {value}
                        </button>
                    ))}
                </div>

                {/* Search Inputs */}
                <div className="grid grid-cols-2 gap-3">
                    <input
                        type="date"
                        placeholder="Từ ngày"
                        className="form-control"
                        value={localFilters.startDate}
                        onChange={(e) => handleFilterChange('startDate', e.target.value)}
                    />
                    <input
                        type="date"
                        placeholder="Đến ngày"
                        className="form-control"
                        value={localFilters.endDate}
                        onChange={(e) => handleFilterChange('endDate', e.target.value)}
                    />
                </div>
                
                <input
                    type="text"
                    placeholder="Tìm theo chú thích"
                    className="form-control"
                    value={localFilters.caption}
                    onChange={(e) => handleFilterChange('caption', e.target.value)}
                />
                
                <input
                    type="text"
                    placeholder="Tìm theo hashtag (cách nhau bởi dấu phẩy)"
                    className="form-control"
                    value={localFilters.hashtag}
                    onChange={(e) => handleFilterChange('hashtag', e.target.value)}
                />
            </div>

            {/* File Grid */}
            <div className="max-h-[calc(100vh-400px)] h-[calc(100vh-400px)] overflow-auto mb-1">
                {fileGenerated.length === 0 && (
                    <div className="text-center text-gray-500 py-8">
                        Không có file {fileType}
                    </div>
                )}
                <div className="grid grid-cols-3 gap-2 content-start mb-1">
                    {fileGenerated.map((item: FileData, index) => (
                        <div key={index} className="relative group">
                            {/* File Preview */}
                            <div className="relative">
                                <FilePreview
                                    file={item}
                                    fileType={fileType}
                                    isActive={fileActive === item.id}
                                    onClick={() => handleFileClick(item)}
                                />
                                
                                {/* Multiple selection checkbox */}
                                {allowMultiple && (
                                    <div className="absolute top-2 left-2">
                                        <input
                                            type="checkbox"
                                            checked={selectedFiles.some(f => f.id === item.id)}
                                            onChange={() => onFileSelect(item)}
                                            className="w-4 h-4"
                                        />
                                        {selectedFiles.some(f => f.id === item.id) && (
                                            <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                                {getFileOrder(item.id ?? 0)}
                                            </span>
                                        )}
                                    </div>
                                )}

                                {/* Deleted overlay */}
                                {item.deleted_at && (
                                    <div
                                        className="absolute w-full h-full top-0 bg-gray-200 opacity-50"
                                        style={{ cursor: 'not-allowed' }}
                                    />
                                )}
                            </div>

                            {/* File info */}
                            <div className="mt-1 text-xs text-gray-600 truncate">
                                {item.file_title || item.file_name}
                            </div>

                            {/* Action dropdown */}
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                    type="button"
                                    className="bg-white rounded-full p-1 shadow-md"
                                    onClick={() => setOpenDropdown(openDropdown === item.id ? null : item.id!)}
                                >
                                    <MoreHorizontal size={16} />
                                </button>
                                
                                {openDropdown === item.id! && (
                                    <div className="absolute right-0 mt-1 bg-white border rounded shadow-lg z-10">
                                        <button
                                            type="button"
                                            className="block w-full px-3 py-2 text-left text-sm hover:bg-gray-100"
                                            onClick={() => {
                                                const link = document.createElement('a');
                                                link.href = item.file_url || '';
                                                link.download = item.file_name || '';
                                                link.click();
                                                setOpenDropdown(null);
                                            }}
                                        >
                                            <Download size={14} className="inline mr-2" />
                                            Tải xuống
                                        </button>
                                        <button
                                            type="button"
                                            className="block w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-gray-100"
                                            onClick={() => {
                                                setFileDeleteId(item.id!);
                                                setShowDeleteModal(true);
                                                setOpenDropdown(null);
                                            }}
                                        >
                                            Xóa
                                        </button>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Pagination */}
            {listFile && (
                <PaginationTable
                    countItem={listFile.totalCount}
                    totalPage={listFile.totalPages}
                    currentPage={listFile.currentPage}
                    handlePageChange={handlePageChange}
                />
            )}

            {/* Delete Confirmation Modal */}
            <ModalConfirm
                show={showDeleteModal}
                text="Bạn có chắc chắn muốn xóa file này không?"
                btnDisabled={deletePending}
                changeShow={(show: boolean) => {
                    setShowDeleteModal(show);
                    if (!show) {
                        setFileDeleteId(null);
                    }
                }}
                submitAction={handleDeleteConfirm}
            />
        </>
    );
}
