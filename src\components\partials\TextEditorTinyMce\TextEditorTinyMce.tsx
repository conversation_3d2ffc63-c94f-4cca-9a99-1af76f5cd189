import { Editor } from '@tinymce/tinymce-react';
import { ARTICLE_TAB, QUERY_KEY, REACT_APP_API_URL, TINY_MCE_API_KEY } from 'constants/common';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Editor as TinyMCEEditor } from 'tinymce';
import { FileResponse, ItemFile, TypeMedia } from 'types/common/Item';
import ImageEditor from '../../../features/article/components/ImageEditor';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import FileService, { FILE_BY_URL } from '../../../services/FileService';
import { ImageEditorSaveData } from '../../../services/ImageEditorService';
import { useAppStore } from '../../../stores/appStore';
import { ActionType } from '../../../types/ImageEditor';
import { showToast } from '../../../utils/common';
import ModalContent from '../ModalContent';
import ModalEditImage from '../ModalEditImage';
import './tinymce.css';

export interface TextEditorTinyMceRef {
    onPreview?: () => void;
}

export interface TextEditorTinyMceProps {
    initialData: string;
    onEditorChange: (value: string) => void;
    setTabAction?(tab: ARTICLE_TAB | null): void;
    setShowModalMedia(s: boolean): void;
    setTypeModalMedia?(t: TypeMedia): void;
    isUtilities?: boolean;
    showModalMedia: boolean;
    onSubmitMedia(urlMedia: string, id?: number): void;
    isArticle?: boolean;
    handleCheckSpellingError?: (content: string) => Promise<void>;
    handleNaturalLanguage?: (content: string) => Promise<void>;
    setShowRelatedArticleModal?: React.Dispatch<React.SetStateAction<boolean>>;
    articleId?: number;
    typeModalMedia?: TypeMedia;
}

const TextEditorTinyMce = forwardRef<TextEditorTinyMceRef, TextEditorTinyMceProps>(function TextEditorTinyMce(
    {
        isUtilities = false,
        initialData,
        onEditorChange,
        setTabAction,
        setTypeModalMedia,
        setShowModalMedia,
        showModalMedia,
        onSubmitMedia,
        isArticle = false,
        handleCheckSpellingError,
        handleNaturalLanguage,
        setShowRelatedArticleModal,
        articleId,
        typeModalMedia,
    },
    ref
) {
    const departmentId = useAppStore((state) => state.departmentId);

    // ImageEditor states
    const [showImageEditor, setShowImageEditor] = useState(false);
    const [editingImageUrl, setEditingImageUrl] = useState('');
    const [editingImageId, setEditingImageId] = useState<number>(0);
    const [editingImageTitle, setEditingImageTitle] = useState('');
    const [isProcessingImage, setIsProcessingImage] = useState(false);
    const [refetchFilesCallback, setRefetchFilesCallback] = useState<(() => void) | null>(null);
    const editingImageUrlRef = useRef('');
    const [editFromImageEditor, setEditFromImageEditor] = useState(false);
    const [selectedImageElement, setSelectedImageElement] = useState<HTMLImageElement | null>(null);
    const [rootImg, setRootImg] = useState<FileResponse | null>(null);

    useEffect(() => {
        editingImageUrlRef.current = editingImageUrl;
    }, [editingImageUrl]);

    const handleUploadImage = () => {
        setTypeModalMedia?.('image');
        setShowModalMedia(true);
    };

    const handleUploadAudio = () => {
        setTypeModalMedia?.('audio');
        setShowModalMedia(true);
    };

    const handleUploadDocument = () => {
        setTypeModalMedia?.('document');
        setShowModalMedia(true);
    };

    const handleUploadAttachment = () => {
        setTypeModalMedia?.('attachment');
        setShowModalMedia(true);
    };

    const editorRef = useRef<TinyMCEEditor | null>(null);

    // Function để extract image URL và title từ clicked image
    const extractImageInfo = (imgElement: HTMLImageElement) => {
        const imageUrl = imgElement.src;
        let imageTitle = '';

        // Tìm title từ alt attribute
        if (imgElement.alt) {
            imageTitle = imgElement.alt;
        }

        // Tìm title từ thẻ p bên dưới image (nếu có)
        const parentDiv = imgElement.closest('div[style*="text-align: center"]');
        if (parentDiv) {
            const titleParagraph = parentDiv.querySelector('p[style*="font-style: italic"]');
            if (titleParagraph && titleParagraph.textContent) {
                imageTitle = titleParagraph.textContent.trim();
            }
        }

        return { imageUrl, imageTitle };
    };

    // Handler để mở ImageEditor
    const handleEditImage = (
        imageUrl: string,
        imageId: number,
        onRefetchCallback?: () => void,
        imageTitle?: string
    ) => {
        setEditingImageUrl(imageUrl);
        setEditingImageId(imageId);
        setEditingImageTitle(imageTitle || '');
        setRefetchFilesCallback(() => onRefetchCallback || null); // Lưu callback để gọi sau khi upload
        setEditFromImageEditor(false); // Đánh dấu là edit từ modal, không phải từ Editor
        setSelectedImageElement(null); // Reset selected image element
        setShowModalMedia(false); // Đóng modal chọn ảnh
        setShowImageEditor(true); // Mở ImageEditor
    };

    // Handler khi save từ ImageEditor
    const handleImageEditorSave = async (data: ImageEditorSaveData) => {
        setIsProcessingImage(true);
        try {
            const editor = editorRef.current;
            if (!editor) {
                showToast(false, ['Không thể truy cập editor']);
                return;
            }

            // Kiểm tra xem ảnh có thay đổi không hoặc có description/tags
            const hasChanges =
                data.rotation !== 0 ||
                data.flipHorizontal ||
                data.flipVertical ||
                data.cropArea ||
                data.watermark ||
                data.textOverlays?.length ||
                data.frame ||
                (data.imageWidth && data.imageHeight) || // Chỉ khi cả width và height đều có giá trị
                (data.description && data.description.trim() !== '') || // Có chú thích
                (data.tags && data.tags.length > 0); // Có tags

            let finalImageUrl = data.imageUrl;
            let uploadResult: { upload: ItemFile } | null = null; // Khai báo để có thể truy cập ở ngoài block

            if (hasChanges) {
                // Nếu có thay đổi, xử lý ảnh với tất cả effects và upload
                try {
                    // Import ImageEditorService để xử lý ảnh
                    const imageEditorService = (await import('../../../services/ImageEditorService')).default;

                    // Tạo ImageState từ data
                    const imageState = {
                        id: '',
                        timestamp: Date.now(),
                        actionType: ActionType.ZOOM,
                        imageUrl: editingImageUrl,
                        originalImageUrl: editingImageUrl,
                        zoomLevel: data.zoomLevel,
                        rotation: data.rotation || 0,
                        flipHorizontal: data.flipHorizontal || false,
                        flipVertical: data.flipVertical || false,
                        cropArea: data.cropArea,
                        watermark: data.watermark,
                        textOverlays: data.textOverlays || [],
                        frame: data.frame,
                        imageWidth: data.imageWidth,
                        imageHeight: data.imageHeight,
                        description: data.description,
                        tags: data.tags,
                    };

                    // Xử lý ảnh với tất cả effects
                    const processedImageUrl = await imageEditorService.processImageWithAllEffects(
                        editingImageUrl,
                        imageState
                    );

                    // Tạo file từ processedImageUrl
                    const response = await fetch(processedImageUrl);
                    const blob = await response.blob();

                    // Tạo tên file mặc định với timestamp (không sử dụng description)
                    const fileName = `edited_image_${Date.now()}.jpg`;
                    const file = new File([blob], fileName, { type: blob.type });

                    // Upload với các thông tin bổ sung, description được lưu vào file_title
                    uploadResult = await FileService.upload(
                        file,
                        'image',
                        departmentId,
                        undefined,
                        undefined,
                        true, // is_newsroom
                        editingImageId, // parent_id
                        data.tags, // file_tags
                        data.description // file_title - chú thích sẽ được lưu vào file_title
                    );

                    if (uploadResult.upload) {
                        finalImageUrl = uploadResult.upload.url;
                        showToast(true, ['Đã lưu và upload ảnh thành công']);

                        // Gọi refetch callback nếu có
                        if (refetchFilesCallback) {
                            refetchFilesCallback();
                        }
                    } else {
                        throw new Error('Upload thất bại');
                    }
                } catch (error) {
                    showToast(false, ['Lỗi khi upload ảnh: ' + error]);
                    return;
                }
            } else {
                // Nếu không có thay đổi, sử dụng ảnh gốc
                finalImageUrl = editingImageUrl;
            }

            // Kiểm tra xem có phải edit từ Editor không
            if (editFromImageEditor && selectedImageElement) {
                // Replace ảnh đã chọn trong Editor
                const uploadedFileTitle = uploadResult?.upload?.file_title;
                const newDescription = data.description || uploadedFileTitle || '';

                // Cập nhật src, data-mce-src và alt của ảnh
                selectedImageElement.src = finalImageUrl;
                selectedImageElement.setAttribute('data-mce-src', finalImageUrl); // Quan trọng: cập nhật data-mce-src
                selectedImageElement.alt = newDescription;

                // Tìm và cập nhật chú thích nếu có
                const parentDiv =
                    selectedImageElement.closest('div[style*="text-align: center"]') ??
                    selectedImageElement.parentElement;
                if (parentDiv) {
                    const titleParagraph = parentDiv.querySelector('p[style*="font-style: italic"]');
                    if (titleParagraph && newDescription) {
                        titleParagraph.textContent = newDescription;
                    } else if (!titleParagraph && newDescription) {
                        // Tạo chú thích mới nếu chưa có
                        const newTitleP = document.createElement('p');
                        newTitleP.style.fontStyle = 'italic';
                        newTitleP.style.color = '#666';
                        newTitleP.style.marginTop = '0.5em';
                        newTitleP.style.textAlign = 'center';
                        newTitleP.textContent = newDescription;
                        parentDiv.appendChild(newTitleP);
                    }
                }

                // Đồng bộ với TinyMCE sau khi thay đổi DOM
                editor.nodeChanged(); // Thông báo cho TinyMCE rằng DOM đã thay đổi

                // Sử dụng setTimeout để đảm bảo TinyMCE đã đồng bộ DOM changes
                setTimeout(() => {
                    editor.save(); // Lưu content từ editor vào form/textarea ẩn

                    // Trigger callback với content mới để cập nhật state
                    const updatedContent = editor.getContent();
                    onEditorChange(updatedContent);

                    // Fire change event để đảm bảo các listener khác được thông báo
                    editor.fire('change');
                }, 0); // Sử dụng timeout 0 để đưa vào event queue
            } else {
                // Thêm ảnh mới vào editor tại vị trí con trỏ (logic cũ)
                let imgHtml: string;

                // Kiểm tra xem có file_title từ upload response không
                const uploadedFileTitle = uploadResult?.upload?.file_title;

                if (uploadedFileTitle) {
                    imgHtml = `<div style="text-align: center; margin: 1em 0;">
                            <img src="${finalImageUrl}" alt="${
                        data.description || ''
                    }" style="max-width: 100%; height: auto;" />
                            <p style="font-style: italic; color: #666; margin-top: 0.5em;">${uploadedFileTitle}</p>
                        </div>`;
                } else {
                    imgHtml = `<p style="text-align: center;"><img src="${finalImageUrl}" alt="${
                        data.description || ''
                    }" style="max-width: 100%; height: auto;" /></p>`;
                }

                editor.insertContent(imgHtml);
            }

            // Đóng ImageEditor
            setShowImageEditor(false);
            setEditingImageUrl('');
            setEditingImageId(0);
            setEditingImageTitle('');
            setRefetchFilesCallback(null); // Reset callback
            setEditFromImageEditor(false); // Reset edit from editor state
            setSelectedImageElement(null); // Reset selected image element
        } catch (error) {
            showToast(false, ['Có lỗi xảy ra khi xử lý ảnh']);
            // console.error('Error in handleImageEditorSave:', error);
        } finally {
            setIsProcessingImage(false);
        }
    };

    // Handler khi cancel từ ImageEditor
    const handleImageEditorCancel = () => {
        setShowImageEditor(false);
        setEditingImageUrl('');
        setEditingImageId(0);
        setEditingImageTitle('');
        setRefetchFilesCallback(null); // Reset callback
        setEditFromImageEditor(false); // Reset trạng thái edit từ ImageEditor
        setSelectedImageElement(null); // Reset selected image element
        editingImageUrlRef.current = ''; // Reset ref
        !editFromImageEditor && setShowModalMedia(true); // Mở lại modal chọn ảnh
        setRootImg(null);
    };

    // Handler cho multiple files submission
    const handleMultipleFilesSubmit = (selectedFiles: { url: string; id: number; title: string; fileType: string; fileName?: string }[]) => {
        // set avatar1, avatar2
        if (typeModalMedia === 'avatar1' || typeModalMedia === 'avatar2') {
            onSubmitMedia(selectedFiles[0].url, selectedFiles[0].id);
            return;
        }

        const editor = editorRef.current;
        if (!editor) {
            showToast(false, ['Không thể truy cập editor']);
            return;
        }

        // Generate HTML based on file type
        const fileHtmls = selectedFiles.map((file) => {
            const title = file.title || '';
            const displayName = title || file.fileName || '';

            switch (file.fileType) {
                case 'image':
                    return `<div style="text-align: center; margin: 1em 0;">
                        <img src="${file.url}" alt="${title}" style="max-width: 100%; height: auto;" />
                        ${title ? `<p style="font-style: italic; color: #666; margin-top: 0.5em;">${title}</p>` : ''}
                    </div>`;

                case 'video':
                    return `<div style="text-align: center; margin: 1em 0;">
                        <video controls style="max-width: 100%; height: auto;">
                            <source src="${file.url}" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        ${title ? `<p style="font-style: italic; color: #666; margin-top: 0.5em;">${title}</p>` : ''}
                    </div>`;

                case 'audio':
                    return `<div style="margin: 1em 0;">
                        ${title ? `<p style="font-weight: bold; margin-bottom: 0.5em;">${title}</p>` : ''}
                        <audio controls style="width: 100%;">
                            <source src="${file.url}" type="audio/mpeg">
                            Your browser does not support the audio element.
                        </audio>
                    </div>`;

                case 'document':
                case 'attachment':
                    return `<p style="margin: 0;">
                            <a href="${file.url}" target="_blank" style="text-decoration: none; color: #007cba;">
                                ${displayName || 'Tài liệu'}
                            </a>
                        </p>`;

                default:
                    return `<p><a href="${file.url}" target="_blank">${displayName}</a></p>`;
            }
        });

        const combinedHtml = fileHtmls.join('\n');

        // Insert at cursor position
        editor.insertContent(combinedHtml);

        // Close modal
        setShowModalMedia(false);

        const fileTypeName = typeModalMedia === 'image' ? 'ảnh' :
                           typeModalMedia === 'video' ? 'video' :
                           typeModalMedia === 'audio' ? 'audio' :
                           typeModalMedia === 'document' ? 'tài liệu' : 'file';

        showToast(true, [`Đã thêm ${selectedFiles.length} ${fileTypeName} vào nội dung`]);
    };

    useImperativeHandle(ref, () => ({
        onPreview: () => {
            if (editorRef.current) editorRef.current.execCommand('mcePreview');
        },
        getEditorRef: () => editorRef.current,
    }));

    const handleImageClick = (imageUrl: string, imageTitle: string, imgElement?: HTMLImageElement) => {
        setEditingImageUrl(imageUrl);
        setEditingImageTitle(imageTitle);
        setSelectedImageElement(imgElement || null);
    };

    const { data: imgSelectedData } = useGraphQLQuery<{ files_by_url: FileResponse }>(
        [QUERY_KEY.FILE_BY_URL, editingImageUrl, selectedImageElement],
        FILE_BY_URL,
        {
            file_url: editingImageUrl.replace(`${REACT_APP_API_URL}/files`, 'uploads'),
        },
        '',
        {
            enabled: !!editingImageUrl && !!selectedImageElement,
        }
    );
    useEffect(() => {
        if (imgSelectedData?.files_by_url) {
            setEditingImageId(imgSelectedData.files_by_url.id!);
            setRootImg(imgSelectedData.files_by_url.parent!);
        }
    }, [imgSelectedData]);

    const handleEditRootImage = (rootImg: FileResponse) => {
        setEditingImageUrl(rootImg.file_url);
        setEditingImageId(rootImg.id!);
        setEditingImageTitle(rootImg.file_title || '');
    };

    return (
        <>
            <Editor
                apiKey={TINY_MCE_API_KEY}
                value={initialData ?? ''}
                onEditorChange={(value) => {
                    if (onEditorChange) {
                        onEditorChange(value);
                    }
                }}
                init={{
                    height: 'calc(100vh - 184px)',
                    plugins: [
                        'contextmenu',
                        'customImage',
                        'insertdatetime',
                        'code',
                        'directionality',
                        'autosave',
                        'accordion',
                        'anchor',
                        'autolink',
                        'charmap',
                        'codesample',
                        'emoticons',
                        'image',
                        'link',
                        'lists',
                        'advlist',
                        'media',
                        'searchreplace',
                        'table',
                        'visualblocks',
                        'wordcount',
                        'fullscreen',
                        'help',
                        'nonbreaking',
                        'pagebreak',
                        'preview',
                        'quickbars',
                        'save',
                        'importcss',
                    ],
                    toolbar:
                        'spellcheck | naturalLanguage | utilities | relatedArticle | undo redo | styleselect fontfamily fontsizeinput | blocks   | forecolor backcolor | bold italic underline strikethrough superscript subscript blockquote | ltr rtl | link customImage media uploadAudio uploadDocument uploadAttachment table | align lineheight | numlist bullist indent outdent | emoticons charmap hr pagebreak | accordion insertdatetime nonbreaking | fullscreen preview save restoredraft template | removeformat',
                    fontsize_input: true,
                    menubar: 'file edit view insert format tools table help',
                    toolbar_mode: 'sliding',
                    quickbars_insert_toolbar:
                        'bold italic underline forecolor backcolor quicklink blockquote customImage',
                    quickbars_selection_toolbar:
                        'bold italic underline forecolor backcolor quicklink blockquote customImage',
                    contextmenu: 'customImageMenu',
                    content_css: './tinymce.css',
                    //@ts-ignore
                    setup: (editor) => {
                        editor.ui.registry.addIcon(
                            'audio',
                            `                 
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-volume-2"><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"></polygon><path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"></path></svg>
                        `
                        );
                        editor.ui.registry.addButton('uploadAudio', {
                            tooltip: 'Upload Audio',
                            icon: 'audio',
                            onAction: () => {
                                handleUploadAudio();
                            },
                        });
                        editor.ui.registry.addButton('uploadDocument', {
                            tooltip: 'Upload Document',
                            icon: 'document',
                            onAction: () => {
                                handleUploadDocument();
                            },
                        });
                        editor.ui.registry.addButton('uploadAttachment', {
                            tooltip: 'Upload Attachment',
                            icon: 'attachment',
                            onAction: () => {
                                handleUploadAttachment();
                            },
                        });
                        isUtilities &&
                            editor.ui.registry.addButton('utilities', {
                                tooltip: 'utilities',
                                text: 'Tiện ích',
                                onAction: () => {
                                    setTabAction?.(ARTICLE_TAB.UTILITIES);
                                },
                            });
                        editor.ui.registry.addButton('previewContent', {
                            tooltip: 'Preview',
                            icon: 'preview',
                            onAction: () => {
                                editor.execCommand('mcePreview');
                            },
                        });
                        editor.addCommand('customImage', function () {
                            handleUploadImage();
                        });
                        editor.ui.registry.addButton('customImage', {
                            icon: 'image',
                            tooltip: 'Insert/edit image',
                            onAction: function () {
                                editor.execCommand('customImage');
                            },
                        });
                        editor.ui.registry.addMenuItem('customImageAction', {
                            icon: 'image',
                            text: 'Insert/edit image',
                            onAction: function () {
                                editor.execCommand('customImage');
                            },
                        });
                        editor.ui.registry.addContextMenu('customImageMenu', {
                            // tslint:disable-next-line: no-any
                            update: function (element: any) {
                                return element.nodeName === 'IMG' ? ['link', 'customImageAction'] : [];
                            },
                        });
                        handleCheckSpellingError &&
                            editor.ui.registry.addButton('spellcheck', {
                                tooltip: 'Check lỗi chính tả',
                                text: 'Check lỗi chính tả',
                                onAction: () => handleCheckSpellingError(editor.getContent()),
                            });
                        handleNaturalLanguage &&
                            editor.ui.registry.addButton('naturalLanguage', {
                                tooltip: 'Check ngôn ngữ tự nhiên',
                                text: 'Check ngôn ngữ tự nhiên',
                                onAction: () => handleNaturalLanguage(editor.getContent()),
                            });
                        setShowRelatedArticleModal &&
                            editor.ui.registry.addButton('relatedArticle', {
                                tooltip: 'Chèn bài viết liên quan',
                                text: 'Chèn bài viết liên quan',
                                onAction: () => setShowRelatedArticleModal(true),
                            });

                        // Thêm event listener cho double click vào image
                        editor.on('dblclick', (e) => {
                            const target = e.target as HTMLElement;
                            if (target.tagName === 'IMG') {
                                const imgElement = target as HTMLImageElement;
                                const { imageUrl, imageTitle } = extractImageInfo(imgElement);
                                handleImageClick(imageUrl, imageTitle, imgElement);
                                setShowImageEditor(true);
                                setEditFromImageEditor(true);
                            }
                        });
                    },
                }}
                onInit={(evt, editor) => {
                    editorRef.current = editor;
                }}
            />
            <ModalContent
                show={showModalMedia}
                changeShow={(s: boolean) => setShowModalMedia(s)}
                title="Chọn media từ thư viện"
                content={
                    <ModalEditImage
                        onSubmit={onSubmitMedia}
                        onSubmitMultiple={handleMultipleFilesSubmit}
                        isArticle={isArticle}
                        show={showModalMedia}
                        onEditImage={handleEditImage}
                        articleId={articleId}
                        fileType={typeModalMedia === 'avatar1' || typeModalMedia === 'avatar2' ? 'image' : 'attachment'}
                        allowMultiple={typeModalMedia !== 'avatar1' && typeModalMedia !== 'avatar2'}
                        showInArticleFilter={isArticle}
                        showAllFileTabs={typeModalMedia !== 'avatar1' && typeModalMedia !== 'avatar2'}
                        restrictToImageOnly={typeModalMedia === 'avatar1' || typeModalMedia === 'avatar2'}
                    />
                }
            />

            {/* ImageEditor Modal */}
            <ModalContent
                show={showImageEditor}
                changeShow={(s: boolean) => {
                    if (!s) handleImageEditorCancel();
                }}
                title="Chỉnh sửa ảnh"
                modalSize="xl"
                content={
                    isProcessingImage ? (
                        <div className="flex items-center justify-center p-8">
                            <div className="text-center">
                                <div className="spinner-border text-primary mb-3" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <p>Đang xử lý ảnh...</p>
                            </div>
                        </div>
                    ) : (
                        <ImageEditor
                            imageUrl={editingImageUrl}
                            departmentId={departmentId}
                            onSave={handleImageEditorSave}
                            onCancel={handleImageEditorCancel}
                            initialDescription={editingImageTitle}
                            rootImg={rootImg}
                            onEditRootImage={handleEditRootImage}
                        />
                    )
                }
            />
        </>
    );
});
export default TextEditorTinyMce;
