import classNames from 'classnames';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FileData, FileQueryRes, ImageTypes, VideoTypes } from 'types/UploadFile';
import { showToast } from 'utils/common';
import { LIMIT_MAX, PAGE_NUMBER_DEFAULT, QUERY_KEY } from '../../../constants/common';
import { FILE_TYPES, FileType, MIME_TYPES, getFileTypeFromMimeType, getFileDisplayName } from '../../../constants/fileTypes';
import { useGraphQLMutation } from '../../../hooks/useGraphQLMutation';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { ARTICLE_FILES_LIST } from '../../../services/ArticleService';
import FileService, { FILE_CHANGE_TITLE, FILE_DELETE, FILES_LIST } from '../../../services/FileService';
import { useAppStore } from '../../../stores/appStore';
import { useAuthStore } from '../../../stores/authStore';
import { ArticleFileQueryRes } from '../../../types/ArticleFile';
import { UserRole } from '../../../types/User';
import FileUploadImage from '../FileUploadImage';
import UpdateButton from '../UpdateButton';
import ImageSearchTab from './ImageSearchTab';
import UniversalFileSearchTab from './UniversalFileSearchTab';
import GenericFileSearchTab from './GenericFileSearchTab';

interface IProps {
    onSubmit(urlMedia: string, id?: number): void;
    onSubmitMultiple?(selectedFiles: { url: string; id: number; title: string; fileType: string; fileName?: string }[]): void; // Callback cho multiple files
    isArticle?: boolean;
    show?: boolean; // Prop để biết khi modal được mở
    fileType?: 'image' | 'video' | 'audio' | 'document' | 'attachment'; // Loại file cần chọn
    onEditImage?: (imageUrl: string, imageId: number, onRefetchCallback?: () => void, imageTitle?: string) => void; // Callback để mở ImageEditor
    articleId?: number;
    onRefetchFiles?: () => void; // Callback để refetch files list
    allowMultiple?: boolean; // Kiểm soát single/multiple selection
    showInArticleFilter?: boolean; // Kiểm soát hiển thị filter "Trong bài viết"
    showAllFileTabs?: boolean; // Hiển thị tất cả tabs cho các loại file
    restrictToImageOnly?: boolean; // Hạn chế chỉ upload ảnh (cho avatar1, avatar2)
}

export enum FileCategory {
    IN_ARTICLE = 1,
    IS_PERSONAL = 2,
    IS_PUBLIC = 3,
}

const listFileCategories = {
    [FileCategory.IN_ARTICLE]: 'Trong bài viết',
    [FileCategory.IS_PERSONAL]: 'Cá nhân',
    [FileCategory.IS_PUBLIC]: 'Toàn bộ',
};

export interface FileWithTitle {
    file: File;
    title: string;
    id: number;
    previewUrl: string;
    url: string; // URL từ server response
    isChecked: boolean; // Trạng thái checkbox
    isTitleSaved: boolean; // Đánh dấu title đã được lưu
}

export default function ModalEditImage({
    onSubmit,
    onSubmitMultiple,
    isArticle = false,
    show = false,
    fileType = 'image',
    onEditImage,
    articleId,
    onRefetchFiles,
    allowMultiple = true,
    showInArticleFilter = false,
    showAllFileTabs = false,
    restrictToImageOnly = false,
}: Readonly<IProps>) {
    const currentUser = useAuthStore((state) => state.user);
    const departmentId = useAppStore((state) => state.departmentId);
    const [tab, setTab] = useState(1);
    const [percent, setPercent] = useState(0);
    const [urlMedia, setUrlMedia] = useState<string[]>([]);
    const [imageActive, setImageActive] = useState(0);
    const [uploadedImage, setUploadedImage] = useState(false);
    const [fileUploadKey, setFileUploadKey] = useState(0);
    const { t } = useTranslation();

    // Map file type to tab number
    const getTabFromFileType = (type: string) => {
        switch (type) {
            case 'image': return 2;
            case 'video': return 3;
            case 'audio': return 4;
            case 'document': return 5;
            case 'attachment': return 6;
            default: return 2;
        }
    };

    // Map tab number to file type
    const getFileTypeFromTab = (tabNumber: number) => {
        switch (tabNumber) {
            case 2: return 'image';
            case 3: return 'video';
            case 4: return 'audio';
            case 5: return 'document';
            case 6: return 'attachment';
            default: return 'image';
        }
    };

    // State management per tab using FileType as key
    const [fileCategoryByTab, setFileCategoryByTab] = useState<Record<FileType, number>>({} as Record<FileType, number>);
    const [searchFiltersByTab, setSearchFiltersByTab] = useState<Record<FileType, { startDate: string; endDate: string; caption: string; hashtag: string }>>({} as Record<FileType, { startDate: string; endDate: string; caption: string; hashtag: string }>);
    const [page, setPage] = useState(PAGE_NUMBER_DEFAULT);

    // Helper functions to get/set state by fileType
    const getCurrentFileType = (): FileType => {
        return getFileTypeFromTab(tab) as FileType;
    };

    const getFileCategoryForCurrentTab = (): number => {
        const currentFileType = getCurrentFileType();
        return fileCategoryByTab[currentFileType] ?? FileCategory.IS_PUBLIC;
    };

    const setFileCategoryForCurrentTab = (category: number) => {
        const currentFileType = getCurrentFileType();
        setFileCategoryByTab(prev => ({ ...prev, [currentFileType]: category }));
    };

    const getSearchFiltersForCurrentTab = () => {
        const currentFileType = getCurrentFileType();
        return searchFiltersByTab[currentFileType] ?? {
            startDate: '',
            endDate: '',
            caption: '',
            hashtag: '',
        };
    };

    const setSearchFiltersForCurrentTab = (filters: { startDate: string; endDate: string; caption: string; hashtag: string }) => {
        const currentFileType = getCurrentFileType();
        setSearchFiltersByTab(prev => ({ ...prev, [currentFileType]: filters }));
    };

    // Get current state for active tab
    const fileCategory = getFileCategoryForCurrentTab();
    const searchFilters = getSearchFiltersForCurrentTab();
    const [selectedImages, setSelectedImages] = useState<{ id: number; url: string; order: number; title: string; mime_type: string; fileName: string }[]>(
        []
    );
    const [uploadedFiles, setUploadedFiles] = useState<FileWithTitle[]>([]);
    const [isAllChecked, setIsAllChecked] = useState(true);
    const [isSaveFileLoading, setIsSaveFileLoading] = useState(false);

    useEffect(() => {
        if (!show) {
            setUrlMedia([]);
            setUploadedImage(false);
            setPercent(0);
            setImageActive(0);
            setSelectedImages([]); // Reset selected images
            setUploadedFiles([]); // Reset uploaded files
            setIsAllChecked(true); // Reset check all state
            // Force re-render FileUploadImage component để reset preview
            setFileUploadKey((prev) => prev + 1);
        }
    }, [show]);

    // Update check all state when uploadedFiles changes
    useEffect(() => {
        const allChecked = uploadedFiles.length > 0 && uploadedFiles.every((file) => file.isChecked);
        setIsAllChecked(allChecked);
    }, [uploadedFiles]);

    const allowedTypes = useMemo(() => {
        const currentFileType = getFileTypeFromTab(tab);
        return MIME_TYPES[currentFileType as keyof typeof MIME_TYPES] || MIME_TYPES.image;
    }, [tab]);

    const buildFilters = () => {
        const baseFilters = [`department_id:=(${departmentId})`, `mime_type:[](${allowedTypes.join(',')})`];

        if (currentUser?.role_id === UserRole.CONTRIBUTOR) {
            baseFilters.push(`created_by:=(${currentUser.id!})`);
        }

        if (fileCategory === FileCategory.IS_PERSONAL) {
            baseFilters.push(`is_newsroom:=(false)`);
        }

        if (searchFilters.startDate) {
            baseFilters.push(`created_at:>=(${searchFilters.startDate})`);
        }
        if (searchFilters.endDate) {
            baseFilters.push(`created_at:<=(${searchFilters.endDate})`);
        }

        if (searchFilters.caption.trim()) {
            baseFilters.push(`file_name:~(${searchFilters.caption.trim()})`);
        }

        if (searchFilters.hashtag.trim()) {
            const tags = searchFilters.hashtag
                .split(',')
                .map((tag) => tag.trim())
                .filter((tag) => tag);
            if (tags.length > 0) {
                baseFilters.push(`tags.name:[](${tags.join(',')})`);
            }
        }

        return baseFilters.filter(Boolean);
    };

    const { data: fileData, refetch } = useGraphQLQuery<FileQueryRes>(
        [QUERY_KEY.FILES, departmentId, getFileTypeFromTab(tab), page, fileCategory, searchFilters],
        FILES_LIST,
        {
            page,
            // limit: PAGINATION.limit,
            filters: buildFilters(),
            limit: 12,
        },
        '',
        {
            enabled:
                !!departmentId &&
                (fileCategory === FileCategory.IS_PUBLIC || fileCategory === FileCategory.IS_PERSONAL),
        }
    );
    const listFile = fileData?.files_list;

    const buildFilterInArticle = () => {
        const baseFilters = [
            `article.department_id:=(${departmentId})`,
            `file.mime_type:[](${allowedTypes.join(',')})`,
            `article_id:=(${articleId})`,
        ];

        if (searchFilters.startDate) {
            baseFilters.push(`file.created_at:>=(${searchFilters.startDate})`);
        }
        if (searchFilters.endDate) {
            baseFilters.push(`file.created_at:<=(${searchFilters.endDate})`);
        }

        if (searchFilters.caption.trim()) {
            baseFilters.push(`file.file_name:~(${searchFilters.caption.trim()})`);
        }

        if (searchFilters.hashtag.trim()) {
            const tags = searchFilters.hashtag
                .split(',')
                .map((tag) => tag.trim())
                .filter((tag) => tag);
            if (tags.length > 0) {
                baseFilters.push(`file.tags.name:[](${tags.join(',')})`);
            }
        }

        return baseFilters.filter(Boolean);
    };

    const { data: fileDataInArticle, refetch: refetchFilesInArticle } = useGraphQLQuery<ArticleFileQueryRes>(
        [QUERY_KEY.FILES_IN_ARTICLE, departmentId, fileType, fileCategory, searchFilters, articleId],
        ARTICLE_FILES_LIST,
        {
            page: PAGE_NUMBER_DEFAULT,
            limit: LIMIT_MAX,
            filters: buildFilterInArticle(),
        },
        '',
        {
            enabled: !!departmentId && fileCategory === FileCategory.IN_ARTICLE && !!articleId,
        }
    );
    const filesInArticle = useMemo(
        () => fileDataInArticle?.article_files_list.data.map((item) => item.file) || [],
        [fileDataInArticle]
    );

    const onProgress = (percent: number) => {
        setPercent(percent);
    };

    const onUploadImage = async (file: File) => {
        try {
            // Đặt giá trị ban đầu cho percent và reset trạng thái upload
            setPercent(0);
            setUploadedImage(false);

            // Gọi FileService.upload với callback onProgress
            const uploadType = fileType === 'video' ? 'video' : '';
            const response = await FileService.upload(file, uploadType, departmentId, undefined, onProgress);

            if (response.upload) {
                refetch();
                // Upload thành công, đặt percent thành 100%
                setPercent(100);

                // Lưu URL của file đã upload
                setUrlMedia(Array.isArray(response.upload.url) ? response.upload.url : [response.upload.url]);
                setImageActive(response.upload.id || 0);

                // Tạo preview URL và thêm vào uploadedFiles
                const previewUrl = URL.createObjectURL(file);
                const newUploadedFile = {
                    file,
                    title: '',
                    id: response.upload.id || 0,
                    previewUrl,
                    url: response.upload.url || '',
                    isChecked: true, // Mặc định checked
                    isTitleSaved: false, // Chưa lưu title
                };
                setUploadedFiles((prev) => [...prev, newUploadedFile]);

                // Đánh dấu đã upload thành công
                setUploadedImage(true);

                // Hiển thị thông báo thành công
                showToast(true, ['Upload thành công']);
            } else {
                setUploadedImage(false);
                showToast(false, [t('error.common')]);
            }
        } catch {
            // Xử lý lỗi
            setPercent(0);
            setUploadedImage(false);
            showToast(false, [t('error.common')]);
        }
    };

    useEffect(() => {
        if (urlMedia.length === 0) {
            setImageActive(0);
            setUploadedImage(false);
            setPercent(0);
        }
    }, [urlMedia]);

    // Handle clear preview from FileUploadImage
    const handleClearPreview = (index: number) => {
        setUploadedFiles((prev) => {
            const fileToRemove = prev[index];
            if (fileToRemove && fileToRemove.previewUrl.startsWith('blob:')) {
                URL.revokeObjectURL(fileToRemove.previewUrl);
            }
            return prev.filter((_, i) => i !== index);
        });

        // Also update urlMedia if needed
        setUrlMedia((prev) => {
            const updated = [...prev];
            updated.splice(index, 1);
            return updated;
        });
    };

    const onChooseFolderImage = async () => {
        // Set tab dựa trên fileType hiện tại hoặc tab đầu tiên nếu showAllFileTabs
        const targetTab = showAllFileTabs ? 2 : getTabFromFileType(fileType);
        setTab(targetTab);
        setImageActive(0);
    };
    const onChooseFolderVideo = async () => {
        setTab(3);
    };
    const onChooseFolderAudio = async () => {
        setTab(4);
    };

    const onChooseImage = (image: FileData) => {
        setUrlMedia(image.file_url ? [image.file_url] : []);
        setImageActive(image.id ?? 0);
    };

    // Handle multiple image selection with checkbox
    const handleImageSelect = (image: FileData) => {
        const imageId = image.id ?? 0;
        const imageUrl = image.file_url ?? '';

        setSelectedImages((prev) => {
            const existingIndex = prev.findIndex((item) => item.id === imageId);

            if (existingIndex >= 0) {
                // Remove image and reorder
                const newSelected = prev.filter((item) => item.id !== imageId);
                return newSelected.map((item, index) => ({
                    ...item,
                    order: index + 1,
                }));
            } else {
                // Add new image with next order number
                const newOrder = prev.length + 1;
                return [...prev, { id: imageId, url: imageUrl, order: newOrder, title: image.file_title ?? '', mime_type: image.mime_type ?? '', fileName: image.file_name ?? '' }];
            }
        });
    };

    // Get order number for an image
    const getImageOrder = (imageId: number): number => {
        const found = selectedImages.find((item) => item.id === imageId);
        return found ? found.order : 0;
    };

    // Handle submit multiple images
    const handleSubmitMultiple = () => {
        if (tab === 1) {
            // Tab upload: sử dụng uploadedFiles
            const checkedFiles = uploadedFiles.filter((file) => file.isChecked);
            if (checkedFiles.length === 0) return;

            const imagesToSubmit = checkedFiles.map((file) => ({
                url: file.url,
                id: file.id,
                title: file.title,
                fileType: getFileTypeFromMimeType(file.file.type),
                fileName: file.file.name,
            }));

            if (onSubmitMultiple) {
                onSubmitMultiple(imagesToSubmit);
            }
        } else {
            // Tab thư viện: sử dụng selectedImages như cũ
            if (selectedImages.length === 0) return;

            const sortedImages = selectedImages
                .sort((a, b) => a.order - b.order)
                .map((item) => ({
                    url: item.url,
                    id: item.id,
                    title: item.title,
                    fileType: getFileTypeFromMimeType(item.mime_type),
                    fileName: item.fileName,
                }));

            if (onSubmitMultiple) {
                onSubmitMultiple(sortedImages);
            }

            // Reset selected images after submit
            setSelectedImages([]);
        }
    };

    // Dynamic text based on file type
    const getFileTypeText = () => {
        const currentFileType = getFileTypeFromTab(tab);
        const displayName = getFileDisplayName(currentFileType as FileType);
        return {
            uploadTab: 'Tải lên',
            libraryTab: `Tìm kiếm ${displayName.toLowerCase()}`,
            useButtonText: `Sử dụng ${displayName.toLowerCase()} này`,
            useButtonTextLibrary: 'Sử dụng',
        };
    };

    const fileTypeText = getFileTypeText();

    const handlePageChange = (event: React.ChangeEvent<unknown>, page: number) => {
        setPage(page);
    };

    // Function để refetch files list từ bên ngoài
    const handleRefetchFiles = () => {
        refetch();
        if (onRefetchFiles) {
            onRefetchFiles();
        }
    };

    const deleteMutation = useGraphQLMutation<{ file_delete: boolean }, { file_id: number }>(FILE_DELETE, '', {
        onSuccess: () => {
            showToast(true, ['Xóa file thành công']);
            if (fileCategory === FileCategory.IN_ARTICLE) {
                refetchFilesInArticle();
            } else {
                refetch();
            }
        },
    });

    const onDeleteImage = (imageId: number) => {
        deleteMutation.mutate({ file_id: imageId });
    };

    // Function to download multiple images as ZIP
    const handleDownloadMultiple = async () => {
        if (selectedImages.length === 0) {
            showToast(false, ['Vui lòng chọn ít nhất một ảnh để tải xuống']);
            return;
        }

        try {
            const zip = new JSZip();
            const promises: Promise<void>[] = [];

            // Add each selected image to ZIP
            selectedImages.forEach((image, index) => {
                const promise = fetch(image.url)
                    .then((response) => response.blob())
                    .then((blob) => {
                        // Get file extension from URL or use jpg as default
                        const urlParts = image.url.split('.');
                        const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1].split('?')[0] : 'jpg';

                        // Create filename with order number
                        const filename = `image_${String(index + 1).padStart(2, '0')}.${extension}`;
                        zip.file(filename, blob);
                    })
                    .catch(() => {
                        // Silently handle individual image download errors
                    });

                promises.push(promise);
            });

            // Wait for all images to be added to ZIP
            await Promise.all(promises);

            // Generate ZIP file
            const zipBlob = await zip.generateAsync({ type: 'blob' });

            // Create download link
            const link = document.createElement('a');
            const zipUrl = window.URL.createObjectURL(zipBlob);
            link.href = zipUrl;
            link.download = `images_${new Date().getTime()}.zip`;
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            window.URL.revokeObjectURL(zipUrl);

            showToast(true, [`Đã tải xuống ${selectedImages.length} ảnh thành công`]);
        } catch {
            showToast(false, ['Có lỗi xảy ra khi tải xuống ảnh']);
        }
    };

    const changeFileTitleMutation = useGraphQLMutation<{}, { file_id: number; file_title: string }>(
        FILE_CHANGE_TITLE,
        '',
        {
            onSuccess: () => {
                // showToast(true, ['Cập nhật chú thích file thành công']);
                // refetch();
                // !!articleId && refetchFilesInArticle();
            },
        }
    );

    const handleTitleChange = (index: number, title: string) => {
        setUploadedFiles((prev) => {
            const updated = [...prev];
            if (updated[index]) {
                updated[index] = { ...updated[index], title };
            }
            return updated;
        });
    };

    const handleSaveTitle = (title: string, fileId: number) => {
        if (fileId && title) {
            changeFileTitleMutation.mutate({ file_id: fileId, file_title: title });
            // Đánh dấu title đã được lưu
            setUploadedFiles((prev) =>
                prev.map((file) => (file.id === fileId ? { ...file, isTitleSaved: true } : file))
            );
        }
    };

    // Handle checkbox change for individual file
    const handleFileCheckboxChange = (index: number, checked: boolean) => {
        setUploadedFiles((prev) => {
            const updated = [...prev];
            if (updated[index]) {
                updated[index] = { ...updated[index], isChecked: checked };
            }
            return updated;
        });
    };

    // Handle check all checkbox
    const handleCheckAllChange = (checked: boolean) => {
        setIsAllChecked(checked);
        setUploadedFiles((prev) => prev.map((file) => ({ ...file, isChecked: checked })));
    };

    const onSaveMultipleFileTitle = async () => {
        try {
            setIsSaveFileLoading(true);
            const checkedFiles = uploadedFiles.filter((file) => file.isChecked);

            await Promise.all(
                checkedFiles.map((f) => changeFileTitleMutation.mutateAsync({ file_id: f.id, file_title: f.title }))
            );

            showToast(true, ['Cập nhật chú thích file thành công']);

            refetch();
            !!articleId && refetchFilesInArticle();
        } catch (error) {
            showToast(false, ['Cập nhật chú thích file thất bại']);
        } finally {
            setIsSaveFileLoading(false);
        }
    };

    return (
        <div className="modal-body">
            <div className="nav-vertical">
                <div className="flex gap-2 h-9">
                    <div
                        className={classNames('p-2 cursor-pointer ', {
                            'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === 1,
                        })}
                        onClick={() => setTab(1)}
                    >
                        Tải lên
                    </div>

                    {showAllFileTabs ? (
                        // Hiển thị tất cả file type tabs khi showAllFileTabs=true
                        <>
                            <div
                                className={classNames('p-2 cursor-pointer ', {
                                    'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === 2,
                                })}
                                onClick={() => setTab(2)}
                            >
                                Hình ảnh
                            </div>
                            <div
                                className={classNames('p-2 cursor-pointer ', {
                                    'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === 3,
                                })}
                                onClick={() => setTab(3)}
                            >
                                Video
                            </div>
                            <div
                                className={classNames('p-2 cursor-pointer ', {
                                    'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === 4,
                                })}
                                onClick={() => setTab(4)}
                            >
                                Âm thanh
                            </div>
                            <div
                                className={classNames('p-2 cursor-pointer ', {
                                    'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === 5,
                                })}
                                onClick={() => setTab(5)}
                            >
                                Tài liệu
                            </div>
                            <div
                                className={classNames('p-2 cursor-pointer ', {
                                    'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === 6,
                                })}
                                onClick={() => setTab(6)}
                            >
                                Đính kèm
                            </div>
                        </>
                    ) : (
                        // Chỉ hiển thị tab cho file type hiện tại khi showAllFileTabs=false
                        <div
                            className={classNames('p-2 cursor-pointer ', {
                                'border-b-2 border-[#A42C48FF] text-[#A42C48FF]': tab === getTabFromFileType(fileType),
                            })}
                            onClick={() => setTab(getTabFromFileType(fileType))}
                        >
                            {getFileDisplayName(fileType as FileType)}
                        </div>
                    )}
                </div>

                <div className="tab-content pt-4">
                    <div className={classNames('tab-pane', { active: tab === 1 })}>
                        <div className={classNames('min-h-[calc(100vh-280px)]')}>
                            <FileUploadImage
                                key={fileUploadKey}
                                type={getFileTypeFromTab(tab)}
                                percent={percent}
                                uploadSuccess={uploadedImage}
                                onFileChange={(file) => onUploadImage(file)}
                                onClearPreview={handleClearPreview}
                                onSaveTitle={handleSaveTitle}
                                uploadedFiles={uploadedFiles}
                                isSavingTitle={changeFileTitleMutation.isPending}
                                onTitleChange={handleTitleChange}
                                isAllChecked={isAllChecked}
                                onCheckAllChange={handleCheckAllChange}
                                onFileCheckboxChange={handleFileCheckboxChange}
                                restrictToImageOnly={restrictToImageOnly}
                            />
                        </div>
                        {uploadedImage && urlMedia && (
                            <div className="modal-footer">
                                <div className="d-flex gap-2">
                                    {fileType === 'image' && (
                                        <>
                                            {/* Button Chỉnh sửa chỉ hiển thị khi có 1 ảnh */}
                                            {uploadedFiles.length === 1 && (
                                                <button
                                                    type="button"
                                                    className="btn btn-outline-primary"
                                                    onClick={() => {
                                                        // Lấy title của ảnh đầu tiên đã được lưu
                                                        const firstFile = uploadedFiles[0];
                                                        const imageTitle = firstFile?.isTitleSaved
                                                            ? firstFile.title
                                                            : '';
                                                        onEditImage?.(
                                                            urlMedia[0] || '',
                                                            imageActive,
                                                            handleRefetchFiles,
                                                            imageTitle
                                                        );
                                                    }}
                                                >
                                                    Chỉnh sửa
                                                </button>
                                            )}

                                            {/* Button Sử dụng ảnh chỉ hiển thị khi có ảnh được checked */}
                                            {uploadedFiles.some((file) => file.isChecked) && (
                                                <div className="flex gap-2 items-center">
                                                    <UpdateButton
                                                        btnText={`Lưu`}
                                                        hasDivWrap={false}
                                                        onSubmit={onSaveMultipleFileTitle}
                                                        isButtonSubmit={false}
                                                        isLoading={isSaveFileLoading}
                                                    />
                                                    <UpdateButton
                                                        btnText={`Sử dụng (${
                                                            uploadedFiles.filter((file) => file.isChecked).length
                                                        }) file`}
                                                        hasDivWrap={false}
                                                        onSubmit={handleSubmitMultiple}
                                                        isButtonSubmit={false}
                                                    />
                                                </div>
                                            )}
                                        </>
                                    )}
                                    {fileType === 'video' && (
                                        <UpdateButton
                                            btnText="Sử dụng"
                                            hasDivWrap={false}
                                            onSubmit={() => {
                                                onSubmit(urlMedia[0] || '', imageActive);
                                            }}
                                            isButtonSubmit={false}
                                        />
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                    {/* Image Tab */}
                    <div className={classNames('tab-pane', { active: tab === 2 })}>
                        <UniversalFileSearchTab
                            listFileCategories={showInArticleFilter ? listFileCategories : { [FileCategory.IS_PERSONAL]: 'Cá nhân', [FileCategory.IS_PUBLIC]: 'Toàn bộ' }}
                            listFile={listFile}
                            imageActive={imageActive}
                            onChooseImage={onChooseImage}
                            handlePageChange={handlePageChange}
                            fileCategory={fileCategory}
                            setFileCategory={setFileCategoryForCurrentTab}
                            searchFilters={searchFilters}
                            setSearchFilters={setSearchFiltersForCurrentTab}
                            filesInArticle={filesInArticle}
                            selectedImages={selectedImages}
                            onImageSelect={handleImageSelect}
                            getImageOrder={getImageOrder}
                            onDeleteImage={onDeleteImage}
                            deletePending={deleteMutation.isPending}
                            fileType={FILE_TYPES.IMAGE}
                        />
                    </div>

                    {/* Video Tab */}
                    <div className={classNames('tab-pane', { active: tab === 3 })}>
                        <UniversalFileSearchTab
                            listFileCategories={showInArticleFilter ? listFileCategories : { [FileCategory.IS_PERSONAL]: 'Cá nhân', [FileCategory.IS_PUBLIC]: 'Toàn bộ' }}
                            listFile={listFile}
                            imageActive={imageActive}
                            onChooseImage={onChooseImage}
                            handlePageChange={handlePageChange}
                            fileCategory={fileCategory}
                            setFileCategory={setFileCategoryForCurrentTab}
                            searchFilters={searchFilters}
                            setSearchFilters={setSearchFiltersForCurrentTab}
                            filesInArticle={filesInArticle}
                            selectedImages={selectedImages}
                            onImageSelect={handleImageSelect}
                            getImageOrder={getImageOrder}
                            onDeleteImage={onDeleteImage}
                            deletePending={deleteMutation.isPending}
                            fileType={FILE_TYPES.VIDEO}
                        />
                    </div>

                    {/* Audio Tab */}
                    <div className={classNames('tab-pane', { active: tab === 4 })}>
                        <UniversalFileSearchTab
                            listFileCategories={showInArticleFilter ? listFileCategories : { [FileCategory.IS_PERSONAL]: 'Cá nhân', [FileCategory.IS_PUBLIC]: 'Toàn bộ' }}
                            listFile={listFile}
                            imageActive={imageActive}
                            onChooseImage={onChooseImage}
                            handlePageChange={handlePageChange}
                            fileCategory={fileCategory}
                            setFileCategory={setFileCategoryForCurrentTab}
                            searchFilters={searchFilters}
                            setSearchFilters={setSearchFiltersForCurrentTab}
                            filesInArticle={filesInArticle}
                            selectedImages={selectedImages}
                            onImageSelect={handleImageSelect}
                            getImageOrder={getImageOrder}
                            onDeleteImage={onDeleteImage}
                            deletePending={deleteMutation.isPending}
                            fileType={FILE_TYPES.AUDIO}
                        />
                    </div>

                    {/* Document Tab */}
                    <div className={classNames('tab-pane', { active: tab === 5 })}>
                        <UniversalFileSearchTab
                            listFileCategories={showInArticleFilter ? listFileCategories : { [FileCategory.IS_PERSONAL]: 'Cá nhân', [FileCategory.IS_PUBLIC]: 'Toàn bộ' }}
                            listFile={listFile}
                            imageActive={imageActive}
                            onChooseImage={onChooseImage}
                            handlePageChange={handlePageChange}
                            fileCategory={fileCategory}
                            setFileCategory={setFileCategoryForCurrentTab}
                            searchFilters={searchFilters}
                            setSearchFilters={setSearchFiltersForCurrentTab}
                            filesInArticle={filesInArticle}
                            selectedImages={selectedImages}
                            onImageSelect={handleImageSelect}
                            getImageOrder={getImageOrder}
                            onDeleteImage={onDeleteImage}
                            deletePending={deleteMutation.isPending}
                            fileType={FILE_TYPES.DOCUMENT}
                        />
                    </div>

                    {/* Attachment Tab */}
                    <div className={classNames('tab-pane', { active: tab === 6 })}>
                        <UniversalFileSearchTab
                            listFileCategories={showInArticleFilter ? listFileCategories : { [FileCategory.IS_PERSONAL]: 'Cá nhân', [FileCategory.IS_PUBLIC]: 'Toàn bộ' }}
                            listFile={listFile}
                            imageActive={imageActive}
                            onChooseImage={onChooseImage}
                            handlePageChange={handlePageChange}
                            fileCategory={fileCategory}
                            setFileCategory={setFileCategoryForCurrentTab}
                            searchFilters={searchFilters}
                            setSearchFilters={setSearchFiltersForCurrentTab}
                            filesInArticle={filesInArticle}
                            selectedImages={selectedImages}
                            onImageSelect={handleImageSelect}
                            getImageOrder={getImageOrder}
                            onDeleteImage={onDeleteImage}
                            deletePending={deleteMutation.isPending}
                            fileType={FILE_TYPES.ATTACHMENT}
                        />
                    </div>
                </div>

                {/* Footer hiển thị cho tất cả các tab search */}
                {tab > 1 && (Boolean(imageActive) || selectedImages.length > 0) && (
                    <div className="modal-footer">
                        <div className="d-flex gap-2">
                            {/* Single file selection button */}
                            {Boolean(imageActive) && (
                                <UpdateButton
                                    btnText={fileTypeText.useButtonTextLibrary}
                                    hasDivWrap={false}
                                    onSubmit={() => onSubmit(urlMedia[0] || '', imageActive)}
                                    isButtonSubmit={false}
                                />
                            )}

                            {/* Multiple files selection button */}
                            {selectedImages.length > 0 && (
                                <div className="flex gap-2">
                                    <button
                                        type="button"
                                        className="btn btn-outline-primary"
                                        onClick={handleDownloadMultiple}
                                    >
                                        Tải xuống
                                    </button>
                                    <UpdateButton
                                        btnText={`Sử dụng (${selectedImages.length} file)`}
                                        hasDivWrap={false}
                                        onSubmit={handleSubmitMultiple}
                                        isButtonSubmit={false}
                                    />
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
