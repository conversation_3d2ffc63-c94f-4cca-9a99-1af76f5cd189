import { debounce } from 'lodash';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { Download, MoreHorizontal, Video, Music, FileText, File } from 'react-feather';
import { FileData } from 'types/UploadFile';
import { DataList } from '../../../types/common';
import ModalConfirm from '../ModalConfirm';
import PaginationTable from '../PaginationTable';
import { FileCategory } from './ModalEditImage';
import { FILE_TYPES, FileType, getFileDisplayName, getFileTypeFromMimeType } from '../../../constants/fileTypes';

interface SearchFilters {
    startDate: string;
    endDate: string;
    caption: string;
    hashtag: string;
}

interface IProps {
    listFileCategories: Record<number, string>;
    listFile: DataList<FileData> | undefined;
    imageActive: number;
    onChooseImage: (image: FileData) => void;
    handlePageChange: (event: ChangeEvent<unknown>, page: number) => void;
    fileCategory: number;
    setFileCategory: (category: number) => void;
    searchFilters: SearchFilters;
    setSearchFilters: (filters: SearchFilters) => void;
    filesInArticle: FileData[];
    selectedImages: { id: number; url: string; order: number; title: string }[];
    onImageSelect: (image: FileData) => void;
    getImageOrder: (imageId: number) => number;
    onDeleteImage: (imageId: number) => void;
    deletePending: boolean;
    fileType: FileType;
}

// Component để render preview của file dựa trên type
const FilePreview = ({
    file,
    fileType,
    isActive,
    onClick
}: {
    file: FileData;
    fileType: FileType;
    isActive: boolean;
    onClick: () => void;
}) => {
    const baseClasses = 'w-full h-auto border-2 rounded-lg transition-all duration-200';
    const activeClasses = isActive ? 'border-[#A42C48FF]' : 'border-gray-200';

    switch (fileType) {
        case FILE_TYPES.IMAGE:
            return (
                <img
                    src={file.file_url || ''}
                    alt={file.file_name || ''}
                    className={`${baseClasses} ${activeClasses} object-cover`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                />
            );

        case FILE_TYPES.VIDEO:
            return (
                <div
                    className={`${baseClasses} ${activeClasses} relative overflow-hidden`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <video
                        src={file.file_url || ''}
                        className="w-full h-full object-cover"
                        preload="metadata"
                        muted
                        onError={(e) => {
                            // Fallback to placeholder if video fails to load
                            const target = e.target as HTMLVideoElement;
                            target.style.display = 'none';
                            const placeholder = target.nextElementSibling as HTMLElement;
                            if (placeholder) placeholder.style.display = 'flex';
                        }}
                    />
                    <div
                        className="absolute inset-0 bg-gray-100 flex items-center justify-center"
                        style={{ display: 'none' }}
                    >
                        <Video size={48} className="text-gray-400" />
                    </div>
                    <div className="absolute bottom-2 left-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center gap-1 max-w-[calc(100%-16px)]">
                        <Video size={14} className="flex-shrink-0" />
                        <span
                            className="truncate"
                            title={file.file_name || ''}
                        >
                            {file.file_name || 'Video'}
                        </span>
                    </div>
                </div>
            );

        case FILE_TYPES.AUDIO:
            return (
                <div
                    className={`${baseClasses} ${activeClasses} flex flex-col items-center justify-center p-4 relative overflow-hidden`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    {/* Waveform background pattern */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-50 to-purple-50">
                        <svg className="w-full h-full opacity-20" viewBox="0 0 200 100">
                            <path d="M0,50 Q25,20 50,50 T100,50 T150,50 T200,50" stroke="currentColor" strokeWidth="2" fill="none" className="text-blue-400"/>
                            <path d="M0,50 Q25,80 50,50 T100,50 T150,50 T200,50" stroke="currentColor" strokeWidth="2" fill="none" className="text-purple-400"/>
                        </svg>
                    </div>
                    <Music size={32} className="text-blue-600 mb-2 relative z-10" />
                    <span className="text-sm font-medium text-center text-gray-700 truncate w-full relative z-10">
                        {file.file_name}
                    </span>
                </div>
            );

        case FILE_TYPES.DOCUMENT:
            return (
                <div
                    className={`${baseClasses} ${activeClasses} flex flex-col items-center justify-center bg-gray-50 p-4`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <FileText size={32} className="text-red-600 mb-2" />
                    <span className="text-sm font-medium text-center text-gray-700 truncate w-full">
                        {file.file_name}
                    </span>
                </div>
            );

        case FILE_TYPES.ATTACHMENT:
        default:
            return (
                <div
                    className={`${baseClasses} ${activeClasses} flex flex-col items-center justify-center bg-gray-50 p-4`}
                    style={{ aspectRatio: '16/9' }}
                    onClick={onClick}
                >
                    <File size={32} className="text-gray-600 mb-2" />
                    <span className="text-sm font-medium text-center text-gray-700 truncate w-full">
                        {file.file_name}
                    </span>
                </div>
            );
    }
};

export default function UniversalFileSearchTab({
    listFileCategories,
    listFile,
    imageActive,
    onChooseImage,
    handlePageChange,
    fileCategory,
    setFileCategory,
    searchFilters,
    setSearchFilters,
    filesInArticle,
    selectedImages,
    onImageSelect,
    getImageOrder,
    onDeleteImage,
    deletePending,
    fileType,
}: Readonly<IProps>) {
    const [openDropdown, setOpenDropdown] = useState<number | null>(null);
    const [localFilters, setLocalFilters] = useState(searchFilters);
    const [fileGenerated, setFileGenerated] = useState<FileData[]>([]);
    const [fileDeleteId, setFileDeleteId] = useState<number | null>(null);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    const debouncedSetSearchFilters = useMemo(
        () => debounce((filters: SearchFilters) => setSearchFilters(filters), 500),
        [setSearchFilters]
    );

    useEffect(() => {
        debouncedSetSearchFilters(localFilters);
        return () => {
            debouncedSetSearchFilters.cancel();
        };
    }, [localFilters, debouncedSetSearchFilters]);

    useEffect(() => {
        if (fileCategory === FileCategory.IN_ARTICLE) {
            setFileGenerated(filesInArticle);
        } else {
            setFileGenerated(listFile?.data ?? []);
        }
    }, [fileCategory, filesInArticle, listFile]);

    const handleFilterChange = (key: keyof SearchFilters, value: string) => {
        setLocalFilters((prev) => ({ ...prev, [key]: value }));
    };



    const handleDeleteConfirm = () => {
        if (fileDeleteId) {
            onDeleteImage(fileDeleteId);
            setShowDeleteModal(false);
            setFileDeleteId(null);
        }
    };

    const handleDownload = async (image: FileData) => {
        if (image.file_url) {
            try {
                const response = await fetch(image.file_url);
                const blob = await response.blob();

                const blobUrl = window.URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.href = blobUrl;
                link.download = image.file_name || 'image';
                document.body.appendChild(link);
                link.click();

                document.body.removeChild(link);
                window.URL.revokeObjectURL(blobUrl);
            } catch (error) {
                const link = document.createElement('a');
                link.href = image.file_url;
                link.download = image.file_name || 'image';
                link.click();
            }
        }
        setOpenDropdown(null);
    };

    return (
        <>
            <div className="space-y-4">
                <div className="space-y-4">
                    {/* Category Radio Buttons */}
                    <div className="flex gap-6">
                        {Object.entries(listFileCategories).map(([key, value]) => (
                            <label key={key} className="flex items-center gap-2 cursor-pointer">
                                <input
                                    type="radio"
                                    name={`category-${fileType}`}
                                    value={key}
                                    checked={fileCategory === parseInt(key)}
                                    onChange={() => setFileCategory(parseInt(key))}
                                    className="w-4 h-4 form-check-input"
                                />
                                <span className="text-sm text-gray-700">{value}</span>
                            </label>
                        ))}
                    </div>

                    {/* Filter Inputs */}
                    <div className="grid grid-cols-4 gap-2">
                        {/* Date Range */}
                        <div>
                            <label htmlFor={`startDate-${fileType}`} className="block text-sm font-medium text-gray-700 mb-1">Thời gian đăng ảnh</label>
                            <input
                                id={`startDate-${fileType}`}
                                name={`startDate-${fileType}`}
                                type="date"
                                value={localFilters.startDate}
                                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                className="form-control"
                            />
                        </div>
                        <div>
                            <label htmlFor={`endDate-${fileType}`} className="block text-sm font-medium text-gray-700 mb-1">&nbsp;</label>
                            <input
                                id={`endDate-${fileType}`}
                                name={`endDate-${fileType}`}
                                type="date"
                                value={localFilters.endDate}
                                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                className="form-control"
                            />
                        </div>

                        {/* Caption */}
                        <div>
                            <label htmlFor={`caption-${fileType}`} className="block text-sm font-medium text-gray-700 mb-1">Chú thích ảnh</label>
                            <input
                                id={`caption-${fileType}`}
                                name={`caption-${fileType}`}
                                type="text"
                                value={localFilters.caption}
                                onChange={(e) => handleFilterChange('caption', e.target.value)}
                                className="form-control"
                                placeholder="Nhập chú thích"
                            />
                        </div>

                        {/* Hashtag */}
                        <div>
                            <label htmlFor={`hashtag-${fileType}`} className="block text-sm font-medium text-gray-700 mb-1">Hashtag</label>
                            <input
                                id={`hashtag-${fileType}`}
                                name={`hashtag-${fileType}`}
                                type="text"
                                value={localFilters.hashtag}
                                onChange={(e) => handleFilterChange('hashtag', e.target.value)}
                                className="form-control"
                                placeholder="hashtag1, hashtag2"
                            />
                        </div>
                    </div>
                </div>
                <div className="max-h-[calc(100vh-400px)] h-[calc(100vh-400px)] overflow-auto mb-1">
                    {fileGenerated.length === 0 && (
                        <div className="text-center text-gray-500 py-8">
                            Không có {getFileDisplayName(fileType).toLowerCase()}
                        </div>
                    )}
                    <div className="grid grid-cols-3 gap-2 content-start mb-1">
                        {fileGenerated.map((item: FileData, index) => (
                            <div key={index} className="relative group">
                                {/* Image Container */}
                                <div className="relative">
                                    <div className="relative">
                                        <FilePreview
                                            file={item}
                                            fileType={getFileTypeFromMimeType(item.mime_type)}
                                            isActive={imageActive === item.id}
                                            onClick={() => {}}
                                        />
                                        {item.deleted_at && (
                                            <div
                                                className="absolute w-full h-full top-0"
                                                style={{
                                                    backgroundColor: '#f8f9fa',
                                                    opacity: '0.5',
                                                    cursor: 'not-allowed',
                                                }}
                                            />
                                        )}
                                    </div>

                                    {/* Checkbox with order number */}
                                    <div className="absolute z-10 top-2 left-2 flex items-center gap-1">
                                        <input
                                            type="checkbox"
                                            checked={selectedImages.some((img) => img.id === (item.id ?? 0))}
                                            onChange={() => onImageSelect(item)}
                                            className="w-4 h-4 form-check-input"
                                            onClick={(e) => e.stopPropagation()}
                                        />
                                        {getImageOrder(item.id ?? 0) > 0 && (
                                            <span className="bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                                                {getImageOrder(item.id ?? 0)}
                                            </span>
                                        )}
                                    </div>

                                    {/* Dropdown Menu */}
                                    <div className="absolute top-2 right-2">
                                        <button
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                setOpenDropdown(openDropdown === item.id ? null : item.id!);
                                            }}
                                            className="px-[4px] py-[.5px] bg-white rounded-full shadow-md hover:bg-gray-50"
                                            type="button"
                                        >
                                            <MoreHorizontal size={12} className="text-gray-600" />
                                        </button>

                                        {/* Dropdown Content */}
                                        {openDropdown === item.id && (
                                            <div className="absolute right-0 top-8 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
                                                <button
                                                    type="button"
                                                    onClick={() => handleDownload(item)}
                                                    className="flex items-center gap-2 w-full p-[4px] text-sm text-gray-700 hover:bg-gray-50"
                                                >
                                                    <Download size={14} />
                                                    Tải xuống
                                                </button>
                                                {/* <button
                                                    type="button"
                                                    onClick={() => handleDelete(item.id ?? 0)}
                                                    className="flex items-center gap-2 w-full p-[4px] text-sm text-red-600 hover:bg-red-50"
                                                >
                                                    <Trash2 size={14} />
                                                    Xóa
                                                </button> */}
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Image Title */}
                                <div className="mt-1">
                                    <h3 className="text-lg font-medium text-gray-900 truncate">
                                        {item.file_title || 'Untitled'}
                                    </h3>
                                    <div className="flex flex-wrap gap-1 mt-1">
                                        {/* Display actual tags from API */}
                                        {item.tags?.map((tag, tagIndex) => (
                                            <span
                                                key={tagIndex}
                                                className="inline-block badge rounded-pill badge-light-danger me-50 mb-50"
                                            >
                                                {tag.name}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                    {fileCategory !== FileCategory.IN_ARTICLE && (
                        <PaginationTable
                            countItem={listFile?.totalCount}
                            totalPage={listFile?.totalPages}
                            currentPage={listFile?.currentPage}
                            handlePageChange={handlePageChange}
                        />
                    )}
                </div>
            </div>
            <ModalConfirm
                show={showDeleteModal}
                text="Bạn có chắc chắn muốn xóa file này không?"
                btnDisabled={deletePending}
                changeShow={(show: boolean) => {
                    setShowDeleteModal(show);
                    if (!show) {
                        setFileDeleteId(null);
                    }
                }}
                submitAction={handleDeleteConfirm}
            />
        </>
    );
}
